<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
        API 接口文档ß
    </title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div id="container">
    <h3>测试企业1的接口文档</h3>
    <h2>接口列表</h2>
    <ul>
                    <li>
                <a href="api_3.htm">M01-墨西哥黑名单查询</a>
            </li>
                    <li>
                <a href="api_4.htm">M11-墨西哥社保(NSS)查询任务提交</a>
            </li>
                    <li>
                <a href="api_5.htm">M12-墨西哥社保(NSS)获取查询结果</a>
            </li>
                    <li>
                <a href="api_6.htm">M15-墨西哥用户标签查询</a>
            </li>
            </ul>

    <h2>接口调用及签名规则</h2>
    <p>
        <a href="basic.htm">请求和签名说明</a>
    </p>

    <h2>源代码及sdk</h2>
    <ul>
        <li>
            <a href="client/javaClient.htm">Java请求工具类</a>
        </li>
        <li>
            <a href="client/pythonClient.htm">Python请求工具类</a>
        </li>
        <li>
            <a href="client/phpClient.htm">PHP请求工具类</a>
        </li>
        <li>
            <a href="client/csClient.htm">C#请求工具类</a>
        </li>
        <li>
            <a href="client/goClient.htm">Go语言请求工具类</a>
        </li>
    </ul>
</div>
</body>
</html>

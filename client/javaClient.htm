<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    API 接口文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    <pre><code>

// package your_package;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

public class HttpApiClient {

    private static final int BUF_LEN = 1024 * 4;

    private static final String CHARSET_NAME = "utf-8";

    private static final Charset CHARSET = Charset.forName(CHARSET_NAME);

    private String appKey;

    private String secret;

    private String url;

    private int timeout = 3000;

    public String execute(String method, Map&lt;String, String&gt; bizParams) throws IOException {
        Map&lt;String, String&gt; requestParams = makeRequestParams(method, bizParams);
        SimpleHttpClient simpleHttpClient = new SimpleHttpClient(timeout);
        return simpleHttpClient.doFormPost(url, null, requestParams);
    }

    public String execute(String method, Map&lt;String, String&gt; bizParams, Map&lt;String, PartData&gt; binaryParams) throws IOException {
        Map&lt;String, String&gt; textParams = new HashMap&lt;String, String&gt;();
        if (bizParams != null) {
            textParams.putAll(bizParams);
        }
        for (Map.Entry&lt;String, PartData&gt; entry : binaryParams.entrySet()) {
            textParams.put(makeDigestName(entry.getKey()), entry.getValue().getMd5Value());
        }

        Map&lt;String, String&gt; requestParams = makeRequestParams(method, textParams);
        SimpleHttpClient simpleHttpClient = new SimpleHttpClient(timeout);

        List&lt;SimpleHttpClient.Part&gt; parts = new ArrayList&lt;SimpleHttpClient.Part&gt;();
        for (Map.Entry&lt;String, String&gt; requestEntry : requestParams.entrySet()) {
            parts.add(new SimpleHttpClient.StringPart(requestEntry.getKey(), requestEntry.getValue()));
        }
        for (Map.Entry&lt;String, PartData&gt; entry : binaryParams.entrySet()) {
            PartData partData = entry.getValue();
            parts.add(new SimpleHttpClient.InputStreamPart(entry.getKey(), "application/octet-stream", partData.getOriginalFileName(), partData.openInputStream()));
        }
        return simpleHttpClient.doMultiPartsRequest(url, null, parts);
    }

    public interface PartData {
        String getMd5Value() throws IOException;

        String getOriginalFileName();

        InputStream openInputStream() throws IOException;
    }

    public static class FilePartData implements PartData {
        private File file;

        public FilePartData(File file) {
            this.file = file;
        }

        public FilePartData(String fileName) {
            this(new File(fileName));
        }

        @Override
        public String getMd5Value() throws IOException {
            FileInputStream inputStream = new FileInputStream(file);
            String md5 = encryptMD5(inputStream);
            close(inputStream);
            return md5;
        }

        @Override
        public String getOriginalFileName() {
            return file.getName();
        }

        @Override
        public InputStream openInputStream() throws IOException {
            return new FileInputStream(file);
        }
    }

    public static class BytesPartData implements PartData {

        private String originalFileName;

        private byte[] bytes;

        public BytesPartData(String originalFileName, byte[] bytes) {
            this.originalFileName = originalFileName;
            this.bytes = bytes;
        }

        public BytesPartData(byte[] bytes) {
            this.originalFileName = "empty";
            this.bytes = bytes;
        }

        @Override
        public String getMd5Value() {
            return encryptMD5(bytes);
        }

        @Override
        public String getOriginalFileName() {
            return originalFileName;
        }

        @Override
        public InputStream openInputStream() {
            return new ByteArrayInputStream(bytes);
        }
    }

    private static String makeDigestName(String name) {
        return name + "Md5";
    }


    private Map&lt;String, String&gt; makeRequestParams(String method, Map&lt;String, String&gt; bizParams) {
        Map&lt;String, String&gt; params = new HashMap&lt;String, String&gt;();
        params.put("appKey", appKey);
        params.put("method", method);
        params.put("timestamp", formatDate(new Date()));
        if (bizParams != null) {
            params.putAll(bizParams);
        }
        String sign = SignUtil.sign(params, secret);
        params.put("sign", sign);
        return params;
    }

    private static String encodeUrl(String s) {
        try {
            return URLEncoder.encode(s, CHARSET_NAME);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }


    private static String copyToString(Reader reader) throws IOException {
        StringBuilder ret = new StringBuilder();
        char[] buf = new char[BUF_LEN];
        while (true) {
            int len = reader.read(buf);
            if (len &lt; 0) {
                break;
            }
            ret.append(buf, 0, len);
        }

        return ret.toString();
    }

    private static final ThreadLocal&lt;SimpleDateFormat&gt; simpleDateFormatThreadLocal = new ThreadLocal&lt;SimpleDateFormat&gt;() {

        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    private static String formatDate(Date date) {
        return simpleDateFormatThreadLocal.get().format(date);
    }

    public HttpApiClient(String appKey, String secret, String url) {
        this.appKey = appKey;
        this.secret = secret;
        this.url = url;
    }

    public static class SimpleHttpClient {

        private int timeout;

        public SimpleHttpClient(int timeout) {
            this.timeout = timeout;
        }

        public String doFormPost(String url, Map&lt;String, String&gt; headers, Map&lt;String, String&gt; formData) throws IOException {
            Map&lt;String, String&gt; newHeaders = new HashMap&lt;String, String&gt;();
            if (headers != null) {
                newHeaders.putAll(headers);
            }
            newHeaders.put("Content-Type", "application/x-www-form-urlencoded;charset=" + CHARSET_NAME);
            byte[] data = makeFormData(formData);
            return doPost(url, newHeaders, new ByteArrayInputStream(data));
        }

        private static byte[] makeFormData(Map&lt;String, String&gt; requestParams) {
            String stringData = joinMap(requestParams, "=", "&amp;", new Transformer&lt;String, String&gt;() {
                public String transform(String object) {
                    return encodeUrl(object);
                }
            }, new Transformer&lt;String, String&gt;() {
                public String transform(String object) {
                    return encodeUrl(object);
                }
            });
            return stringData.getBytes(CHARSET);
        }


        public String doPost(String url, Map&lt;String, String&gt; headers, InputStream data) throws IOException {
            return doRequest("POST", url, headers, data);
        }

        public String doMultiPartsRequest(String url, Map&lt;String, String&gt; userHeaders, List&lt;Part&gt; parts) throws IOException {
            Map&lt;String, String&gt; headers = new HashMap&lt;String, String&gt;();
            if (userHeaders != null) {
                headers.putAll(userHeaders);
            }
            headers.put("Content-Type", "multipart/form-data; boundary=" + MultiPartConstants.BOUNDARY);

            File tempFile = File.createTempFile("tempPart", null);
            FileOutputStream outputStream = new FileOutputStream(tempFile);
            for (Part part : parts) {
                part.writeTo(outputStream);
                close(part);
            }
            outputStream.write(MultiPartConstants.EXTRA_BYTES);
            outputStream.write(MultiPartConstants.BOUNDARY_BYTES);
            outputStream.write(MultiPartConstants.EXTRA_BYTES);
            outputStream.close();
            final InputStream fis = new FileInputStream(tempFile);
            String ret = doRequest("POST", url, headers, fis);
            close(fis);
            tempFile.delete();
            return ret;
        }

        public String doRequest(String method, String url, Map&lt;String, String&gt; userHeaders, InputStream data) throws IOException {
            HttpURLConnection urlConnection = handleRequest(method, url, userHeaders, data);
            Reader reader = new InputStreamReader(urlConnection.getInputStream(), CHARSET);
            try {
                return copyToString(reader);
            } finally {
                close(reader);
            }
        }

        private HttpURLConnection handleRequest(String method, String url, Map&lt;String, String&gt; userHeaders, InputStream data) throws IOException {
            URL urlObject = new URL(url);
            HttpURLConnection urlConnection = (HttpURLConnection) urlObject.openConnection();
            urlConnection.setRequestMethod(method);
            urlConnection.setConnectTimeout(timeout);
            urlConnection.setReadTimeout(timeout);
            urlConnection.setInstanceFollowRedirects(false);
            if (userHeaders != null) {
                for (Map.Entry&lt;String, String&gt; entry : userHeaders.entrySet()) {
                    urlConnection.addRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            if (data != null) {
                int len = data.available();
                urlConnection.addRequestProperty("Content-Length", String.valueOf(len));
                urlConnection.setDoInput(true);
                urlConnection.setDoOutput(true);
                OutputStream outputStream = urlConnection.getOutputStream();
                copyAndClose(data, outputStream);
            }
            return urlConnection;
        }

        public static abstract class Part implements Closeable {

            protected String name;

            protected String contentType;

            protected String contentTransferEncoding;

            public Part(String name, String contentType, String contentTransferEncoding) {
                this.name = name;
                this.contentType = contentType;
                this.contentTransferEncoding = contentTransferEncoding;
            }

            public abstract void writeTo(OutputStream outputStream) throws IOException;

        }

        public static class StringPart extends BytesPart {

            public StringPart(String name, String value) {
                super(name, null, CHARSET_NAME, null, value.getBytes(CHARSET));
            }
        }

        public static class BytesPart extends Part {

            private InputStreamPart inputStreamPart;

            public BytesPart(String name, String contentType, String fileName, byte[] bytes) {
                this(name, contentType, null, fileName, bytes);
            }

            protected BytesPart(String name, String contentType, String contentTransferEncoding, String fileName, byte[] bytes) {
                super(name, contentType, contentTransferEncoding);
                this.inputStreamPart = new InputStreamPart(name, contentType, contentTransferEncoding, fileName, new ByteArrayInputStream(bytes));
            }

            @Override
            public void writeTo(OutputStream outputStream) throws IOException {
                this.inputStreamPart.writeTo(outputStream);
            }

            @Override
            public void close() throws IOException {
                this.inputStreamPart.close();
            }
        }

        public static class InputStreamPart extends Part {

            private String fileName;

            private InputStream inputStream;

            public InputStreamPart(String name, String contentType, String fileName, InputStream inputStream) {
                this(name, contentType, null, fileName, inputStream);
            }

            protected InputStreamPart(String name, String contentType, String contentTransferEncoding, String fileName, InputStream inputStream) {
                super(name, contentType, contentTransferEncoding);
                this.fileName = fileName;
                this.inputStream = inputStream;
            }

            @Override
            public void writeTo(OutputStream outputStream) throws IOException {
                outputStream.write(MultiPartConstants.EXTRA_BYTES);
                outputStream.write(MultiPartConstants.BOUNDARY_BYTES);
                outputStream.write(MultiPartConstants.CRLF_BYTES);

                outputStream.write(MultiPartConstants.CONTENT_DISPOSITION_BYTES);
                outputStream.write(("\"" + name + "\"").getBytes(CHARSET));
                if (!isEmpty(fileName)) {
                    outputStream.write(("; filename=\"" + fileName + "\"").getBytes(CHARSET));
                }
                outputStream.write(MultiPartConstants.CRLF_BYTES);

                if (!isEmpty(contentType)) {
                    outputStream.write((MultiPartConstants.CONTENT_TYPE + contentType).getBytes(CHARSET));
                    outputStream.write(MultiPartConstants.CRLF_BYTES);
                }
                if (!isEmpty(contentTransferEncoding)) {
                    outputStream.write((MultiPartConstants.CONTENT_TRANSFER_ENCODING + contentTransferEncoding).getBytes(CHARSET));
                    outputStream.write(MultiPartConstants.CRLF_BYTES);
                }
                outputStream.write(MultiPartConstants.CRLF_BYTES);
                copy(inputStream, outputStream);
                outputStream.write(MultiPartConstants.CRLF_BYTES);
            }

            @Override
            public void close() throws IOException {
                HttpApiClient.close(inputStream);
            }
        }

        private interface MultiPartConstants {

            String BOUNDARY = "-----RP20140102314159265358979";

            byte[] BOUNDARY_BYTES = BOUNDARY.getBytes(CHARSET);

            String CRLF = "\r\n";

            byte[] CRLF_BYTES = CRLF.getBytes(CHARSET);

            String EXTRA = "--";

            byte[] EXTRA_BYTES = EXTRA.getBytes(CHARSET);

            String CONTENT_DISPOSITION = "Content-Disposition: form-data; name=";

            byte[] CONTENT_DISPOSITION_BYTES = CONTENT_DISPOSITION.getBytes(CHARSET);

            String CONTENT_TYPE = "Content-Type: ";

            String CONTENT_TRANSFER_ENCODING = "Content-Transfer-Encoding: ";

        }

    }


    private static &lt;K, V&gt; String joinMap(Map&lt;K, V&gt; map, String kvCat, String entryCat, Transformer&lt;V, String&gt; valueTransformer, Transformer&lt;K, String&gt; keyTransformer) {
        if (map == null || map.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry&lt;K, V&gt; entry : map.entrySet()) {
            if (first) {
                first = false;
            } else {
                sb.append(entryCat);
            }
            if (keyTransformer != null) {
                String key = keyTransformer.transform(entry.getKey());
                sb.append(key);
            } else {
                sb.append(entry.getKey());
            }
            sb.append(kvCat);
            if (valueTransformer != null) {
                Object o = valueTransformer.transform(entry.getValue());
                sb.append(o);
            } else {
                sb.append(entry.getValue());
            }
        }
        return sb.toString();
    }

    private interface Transformer&lt;S, T&gt; {
        T transform(S object);
    }

    static class SignUtil {
        public static String sign(Map&lt;String, String&gt; params, String secret) {
            String rawParams = genStringForSign(params);
            String rawParamsWithSecret = secret + ";" + rawParams + ";" + secret;
            byte[] data = rawParamsWithSecret.getBytes(CHARSET);
            String md5Upper = encryptMD5(data).toUpperCase();
            return md5Upper;
        }

        private static String genStringForSign(Map&lt;String, String&gt; params) {
            return concatParams(new TreeMap&lt;String, String&gt;(params));
        }

        private static String concatParams(Map&lt;String, String&gt; params) {
            return joinMap(params, "=", "&amp;", null, null);
        }
    }


    private static final ThreadLocal&lt;MessageDigest&gt; messageDigestThreadLocal = new ThreadLocal&lt;MessageDigest&gt;() {
        @Override
        protected MessageDigest initialValue() {
            try {
                return MessageDigest.getInstance("MD5");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    };


    private static String encryptMD5(InputStream inputStream) {
        MessageDigest messageDigest = messageDigestThreadLocal.get();
        messageDigest.reset();
        byte[] buf = new byte[BUF_LEN];
        try {
            while (true) {
                int len = inputStream.read(buf);
                if (len &lt; 0) {
                    break;
                }
                messageDigest.update(buf, 0, len);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        byte[] digest = messageDigest.digest();
        char[] charBuf = new char[16 * 2];
        int k = 0;
        for (int i = 0; i &lt; 16; i++) {
            byte byte0 = digest[i];
            charBuf[k++] = hexDigits[byte0 &gt;&gt;&gt; 4 &amp; 0xf];
            charBuf[k++] = hexDigits[byte0 &amp; 0xf];
        }
        return new String(charBuf);
    }

    private static String encryptMD5(byte[] source) {
        return encryptMD5(new ByteArrayInputStream(source));
    }

    private static char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private static void copyAndClose(InputStream is, OutputStream os) throws IOException {
        copy(is, os);
        close(is);
        close(os);
    }

    private static void copy(InputStream is, OutputStream os) throws IOException {
        byte[] buf = new byte[BUF_LEN];
        while (true) {
            int len = is.read(buf);
            if (len &lt; 0) {
                break;
            }
            os.write(buf, 0, len);
        }
    }

    private static void close(Closeable c) {
        if (c != null) {
            try {
                c.close();
            } catch (Exception e) {
                // ignore
            }
        }
    }

    private static boolean isEmpty(String s) {
        if (s == null || s.length() == 0) {
            return true;
        }
        return false;
    }


    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}
</code></pre>
</div>
</body>
</html>

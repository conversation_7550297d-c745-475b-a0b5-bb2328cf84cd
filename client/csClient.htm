<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    API 接口文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    <pre>
    <code>

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Net.Http;


namespace HttpApiClient
{

    public class PartData
    {
        private string _fileName;
        private byte[] _data;

        public PartData(string fileName, byte[] data)
        {
            _fileName = fileName;
            _data = data;
        }

        public string FileName
        {
            get { return _fileName; }
        }

        public byte[] Data
        {
            get { return _data; }
        }
    }

    public class HttpApiClient
    {
        private string _appKey;
        private string _secret;
        private string _url;
        private int _timeout;

        public HttpApiClient(string appKey, string secret, string url, int timeout = 3000)
        {
            _appKey = appKey;
            _secret = secret;
            _url = url;
            _timeout = timeout;
        }

        public string Execute(string method, Dictionary&lt;string, string&gt; bizParams)
        {
            var sysParameters = new Dictionary&lt;string, string&gt;
            {
                { "appKey", _appKey },
                { "method", method },
                { "timestamp", GetTimestamp() }
            };
            var requestParameters = new Dictionary&lt;string, string&gt;(sysParameters);
            if (bizParams != null)
            {
                foreach (var kvp in bizParams)
                {
                    requestParameters[kvp.Key] = kvp.Value;
                }
            }
            var sign = Sign(requestParameters, _secret);
            requestParameters["sign"] = sign;

            using (var client = new HttpClient(_timeout))
            {
                return client.DoFormPost(_url, requestParameters);
            }
        }

        public string ExecuteMultipart(string method, Dictionary&lt;string, string&gt; bizParams, Dictionary&lt;string, PartData&gt; binaryParams)
        {
            var sysParameters = new Dictionary&lt;string, string&gt;
            {
                { "appKey", _appKey },
                { "method", method },
                { "timestamp", GetTimestamp() }
            };
            var requestParameters = new Dictionary&lt;string, string&gt;(sysParameters);
            if (bizParams != null)
            {
                foreach (var kvp in bizParams)
                {
                    requestParameters[kvp.Key] = kvp.Value;
                }
            }
            foreach (var kvp in binaryParams)
            {
                var digestName = kvp.Key + "Md5";
                var value = GetMd5Hash(kvp.Value.Data);
                requestParameters[digestName] = value;
            }
            var sign = Sign(requestParameters, _secret);
            requestParameters["sign"] = sign;

            var parts = new List&lt;Part&gt;();
            foreach (var kvp in requestParameters)
            {
                parts.Add(new Part(kvp.Key, kvp.Value, new byte[0], false, null));
            }
            foreach (var kvp in binaryParams)
            {
                parts.Add(new Part(kvp.Key, "", kvp.Value.Data, true, kvp.Value.FileName));
            }

            using (var client = new HttpClient(_timeout))
            {
                return client.DoMultipartRequest(_url, parts);
            }
        }

        private string GetTimestamp()
        {
            var localTime = DateTime.Now.ToLocalTime();
            var timestamp = localTime.ToString("yyyy-MM-dd HH:mm:ss");
            return timestamp;
        }

        private string Sign(Dictionary&lt;string, string&gt; parameters, string secret)
        {
            var sortedKeys = new List&lt;string&gt;(parameters.Keys);
            sortedKeys.Sort();

            var signContent = new StringBuilder(secret);
            var first = true;
            foreach (var key in sortedKeys)
            {
                if (first) {
                    first = false;
                    signContent.Append(";");
                } else {
                    signContent.Append("&amp;");
                }
                signContent.Append(key).Append("=").Append(parameters[key]);
            }
            signContent.Append(";").Append(secret);

            using (var md5 = MD5.Create())
            {
                var bytes = Encoding.UTF8.GetBytes(signContent.ToString());
                var hashBytes = md5.ComputeHash(bytes);
                var result = new StringBuilder(hashBytes.Length * 2);

                foreach (var b in hashBytes)
                {
                    result.Append(b.ToString("X2"));
                }

                return result.ToString();
            }
        }

        private static string GetMd5Hash(byte[] data)
        {
            using (var md5 = MD5.Create())
            {
                var hashBytes = md5.ComputeHash(data);
                var hexBuilder = new StringBuilder();
                for (int i = 0; i &lt; hashBytes.Length; i++)
                {
                    hexBuilder.Append(hashBytes[i].ToString("X2"));
                }
                return hexBuilder.ToString();
            }
        }


    }

    class HttpClient : IDisposable
    {
        private int _timeout;

        public HttpClient(int timeout)
        {
            _timeout = timeout;
        }

        public string DoMultipartRequest(string url, List&lt;Part&gt; parts)
        {
            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.Timeout = _timeout;
            var boundary = "-----RP20140102314159265358979";
            request.ContentType = $"multipart/form-data; boundary={boundary}";

            using (var requestStream = request.GetRequestStream())
            {
                foreach (var part in parts)
                {
                    part.WriteTo(requestStream, boundary);
                }
                var endBoundary = $"--{boundary}--";
                var endBoundaryBytes = Encoding.UTF8.GetBytes(endBoundary + "\r\n");
                requestStream.Write(endBoundaryBytes, 0, endBoundaryBytes.Length);
            }

            using (var response = (HttpWebResponse)request.GetResponse())
            {
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception("Invalid HTTP Status Code");
                }
                var resultStream = response.GetResponseStream();
                if (resultStream != null)
                {
                    using (var reader = new StreamReader(resultStream))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }

            throw new Exception("Failed to execute the request");
        }

        public string DoFormPost(string url, Dictionary&lt;string, string&gt; postData)
        {
            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.Timeout = _timeout;
            request.ContentType = "application/x-www-form-urlencoded;charset=UTF-8";
            request.Headers.Add("Cache-Control", "no-cache");

            var body = Encoding.UTF8.GetBytes(UrlEncode(postData));
            using (var requestStream = request.GetRequestStream())
            {
                requestStream.Write(body, 0, body.Length);
            }

            using (var response = (HttpWebResponse)request.GetResponse())
            {
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception($"Invalid HTTP status {response.StatusCode}, detail body: {new StreamReader(response.GetResponseStream()).ReadToEnd()}");
                }

                var resultStream = response.GetResponseStream();
                if (resultStream != null)
                {
                    using (var reader = new StreamReader(resultStream))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }

            throw new Exception("Failed to execute the request");
        }

        private string UrlEncode(Dictionary&lt;string, string&gt; data)
        {
            var stringBuilder = new StringBuilder();
            foreach (var kvp in data)
            {
                stringBuilder.Append($"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}&amp;");
            }
            return stringBuilder.ToString().TrimEnd('&amp;');
        }

        public void Dispose()
        {
            // Dispose any resources if needed
        }
    }

    class Part
    {
        private string _fieldName;
        private string _stringValue;
        private byte[] _bytesData;
        private bool _isBinary;
        private string _fileName;

        public Part(string fieldName, string stringValue, byte[] bytesData, bool isBinary, string fileName)
        {
            _fieldName = fieldName;
            _stringValue = stringValue;
            _bytesData = bytesData;
            _isBinary = isBinary;
            _fileName = fileName;
        }



        public void WriteTo(Stream stream, string boundary)
        {
            var newLine = "\r\n";
            var boundaryBytes = Encoding.UTF8.GetBytes("--" + boundary + newLine);

            stream.Write(boundaryBytes, 0, boundaryBytes.Length);

            if (_isBinary)
            {
                var fileHeader = $"Content-Disposition: form-data; name=\"{_fieldName}\"; filename=\"{_fileName}\"\r\n";
                var contentType = "Content-Type: application/octet-stream\r\n\r\n";
                var headerBytes = Encoding.UTF8.GetBytes(fileHeader + contentType);
                stream.Write(headerBytes, 0, headerBytes.Length);
                stream.Write(_bytesData, 0, _bytesData.Length);
            }
            else
            {
                var fieldHeader = $"Content-Disposition: form-data; name=\"{_fieldName}\"\r\n\r\n";
                var headerBytes = Encoding.UTF8.GetBytes(fieldHeader);
                stream.Write(headerBytes, 0, headerBytes.Length);
                var valueBytes = Encoding.UTF8.GetBytes(_stringValue);
                stream.Write(valueBytes, 0, valueBytes.Length);
            }

            var endBytes = Encoding.UTF8.GetBytes(newLine);
            stream.Write(endBytes, 0, endBytes.Length);
        }
    }
}
</code>
</prev>
</div>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    API 接口文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    <pre>
    <code>
&lt;?php

const _BOUNDARY = "-----RP20140102314159265358979";

function _get_timestamp() {
    date_default_timezone_set('Asia/Shanghai');
    $d = date("Y-m-d H:i:s");
    return $d;
}

function _sign($input_params, $secret) {
    if (is_array($input_params)) {
        ksort($input_params);
        $paramStrings = array();
        foreach ($input_params as $key =&gt; $value) {
            $paramStrings[] = $key . '=' . $value;
        }
        $input_params = $secret . ';' . implode('&amp;', $paramStrings) . ';' . $secret;
    }
    return strtoupper(md5($input_params));
}

class HttpApiClient {
    private $app_key;
    private $secret;
    private $url;
    private $timeout;

    public function __construct($app_key, $secret, $url, $timeout=3000) {
        $this-&gt;app_key = $app_key;
        $this-&gt;secret = $secret;
        $this-&gt;url = $url;
        $this-&gt;timeout = $timeout;
    }

    public function execute($method, $biz_params) {
        $sys_parameters = [
            "appKey" =&gt; $this-&gt;app_key,
            "method" =&gt; $method,
            "timestamp" =&gt; _get_timestamp()
        ];
        $request_parameters = $sys_parameters;
        if (!is_null($biz_params)) {
            $request_parameters = array_merge($request_parameters, $biz_params);
        }
        $sign = _sign($request_parameters, $this-&gt;secret);
        $request_parameters["sign"] = $sign;
        $client = new _HttpClient($this-&gt;timeout);
        return $client-&gt;do_form_post($this-&gt;url, $request_parameters);
    }

    public function execute_multipart($method, $biz_params, $binary_params) {
        $sys_parameters = [
            "appKey" =&gt; $this-&gt;app_key,
            "method" =&gt; $method,
            "timestamp" =&gt; _get_timestamp()
        ];
        $request_parameters = $sys_parameters;
        if (!is_null($biz_params)) {
            $request_parameters = array_merge($request_parameters, $biz_params);
        }
        foreach ($binary_params as $name =&gt; $data) {
            $digest_name = $name . "Md5";
            $value = strtoupper(md5($data[1]));
            $request_parameters[$digest_name] = $value;
        }

        $sign = _sign($request_parameters, $this-&gt;secret);
        $request_parameters["sign"] = $sign;

        $parts = [];
        foreach ($request_parameters as $name =&gt; $value) {
            $parts[] = new _Part($name, $value, false, null);
        }

        foreach ($binary_params as $name =&gt; $data) {
            $parts[] = new _Part($name, $data[1], true, $data[0]);
        }

        $client = new _HttpClient($this-&gt;timeout);

        return $client-&gt;do_multipart_request($this-&gt;url, $parts);
    }
}

class _BytesBody {
    private $body = [];

    public function append_str($s) {
        $this-&gt;body[] = mb_convert_encoding($s, "UTF-8");
    }

    public function append_bin($data) {
        $this-&gt;body[] = $data;
    }

    public function join($sep) {
        return implode($sep, $this-&gt;body);
    }
}

class _Part {
    private $field_name;
    private $value;
    private $is_bin;

    public function __construct($field_name, $value, $is_bin, $file_name) {
        $this-&gt;field_name = $field_name;
        $this-&gt;value = $value;
        $this-&gt;is_bin = $is_bin;
        $this->file_name = $file_name;
    }

    public function write_to($bytes_body) {
        $bytes_body-&gt;append_str('--' . _BOUNDARY);
        if ($this-&gt;is_bin) {
            $bytes_body-&gt;append_str(
                'Content-Disposition: form-data; name="' . $this-&gt;field_name . '"; filename="' . $this-&gt;file_name . '"'
            );
            $bytes_body-&gt;append_str('Content-Type: application/octet-stream');
        } else {
            $bytes_body-&gt;append_str('Content-Disposition: form-data; name="' . $this-&gt;field_name . '"');
        }
        $bytes_body-&gt;append_str('');
        if ($this-&gt;is_bin) {
            $bytes_body-&gt;append_bin($this-&gt;value);
        } else {
            $bytes_body-&gt;append_str($this-&gt;value);
        }
    }
}

class _HttpClient {
    private $timeout;

    public function __construct($timeout) {
        $this-&gt;timeout = $timeout;
    }

    public function do_multipart_request($url, $parts) {
        $headers = array(
            "Content-Type: multipart/form-data; boundary=" . _BOUNDARY
        );
        $body = new _BytesBody();
        foreach ($parts as $part) {
            $part-&gt;write_to($body);
        }
        $body-&gt;append_str('--' . _BOUNDARY . '--');
        $body-&gt;append_str('');
        $body_data = $body-&gt;join("\r\n");
        return $this-&gt;do_request("POST", $url, $body_data, $headers);
    }

    public function do_form_post($url, $post_data) {
        $post_header = array(
            'Content-type: application/x-www-form-urlencoded;charset=UTF-8',
            'Cache-Control: no-cache'
        );
        $body = http_build_query($post_data);
        return $this-&gt;do_request("POST", $url, $body, $post_header);
    }

    private function do_request($method, $url, $body, $headers) {
        $connection = curl_init();
        curl_setopt($connection, CURLOPT_URL, $url);
        curl_setopt($connection, CURLOPT_POST, 1);
        curl_setopt($connection, CURLOPT_POSTFIELDS, $body);
        curl_setopt($connection, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($connection, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($connection, CURLOPT_CONNECTTIMEOUT_MS, $this-&gt;timeout);
        if (!$connection) {
            throw new Exception("Failed to connect: $errstr ($errno)");
        }
        $result = curl_exec($connection);

        if ($result === false) {
            throw new Exception('HTTP request failed: ' . curl_error($connection));
        }

        $status_code = curl_getinfo($connection, CURLINFO_HTTP_CODE);
        if ($status_code !== 200) {
            throw new Exception('Invalid HTTP status code: ' . $status_code);
        }
        curl_close($connection);
        return $result;
    }

}
</code>
</pre>
</div>
</body>
</html>

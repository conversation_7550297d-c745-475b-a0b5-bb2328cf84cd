<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    API 接口文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    <pre>
	<code>
// package your_package

import (
    "bytes"
    "crypto/md5"
    "fmt"
    "io/ioutil"
    "mime/multipart"
    "net/http"
    "net/url"
    "sort"
    "strings"
    "time"
)


func getTimestamp() string {
    t := time.Now().Format("2006-01-02 15:04:05")
    return t
}

func sign(params map[string]string, secret string) string {
    keys := make([]string, 0, len(params))
    for k := range params {
        keys = append(keys, k)
    }
    sort.Strings(keys)

    var sb strings.Builder
    sb.WriteString(secret)
    first := true
    for _, key := range keys {
        if first {
            first = false
            sb.WriteString(";")
        } else {
            sb.WriteString("&amp;")
        }
        sb.WriteString(fmt.Sprintf("%s=%s", key, params[key]))
    }
    sb.WriteString(";")
    sb.WriteString(secret)

    hash := md5.Sum([]byte(sb.String()))
    signature := fmt.Sprintf("%x", hash)
    return strings.ToUpper(signature)
}

type HttpApiClient struct {
    appKey   string
    secret   string
    url      string
    timeout  int
    client   *http.Client
}

type PartData struct {
    OriginalFileName string
    Data []byte
}

func NewHttpApiClient(appKey, secret, url string, timeout int) *HttpApiClient {
    return &amp;HttpApiClient{
        appKey:   appKey,
        secret:   secret,
        url:      url,
        timeout:  timeout,
        client:   &amp;http.Client{Timeout: time.Duration(timeout) * time.Millisecond},
    }
}

func (c *HttpApiClient) Execute(method string, bizParams map[string]string) (string, error) {
    sysParameters := map[string]string{
        "appKey":    c.appKey,
        "method":    method,
        "timestamp": getTimestamp(),
    }
    requestParameters := sysParameters
    if bizParams != nil {
        for key, value := range bizParams {
            requestParameters[key] = value
        }
    }

    signature := sign(requestParameters, c.secret)
    requestParameters["sign"] = signature

    formData := url.Values{}
    for key, value := range requestParameters {
        formData.Set(key, value)
    }

    resp, err := c.client.PostForm(c.url, formData)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return "", err
    }

    return string(body), nil
}

func (c *HttpApiClient) ExecuteMultipart(method string, bizParams map[string]string, binaryParams map[string]PartData) (string, error) {
    sysParameters := map[string]string{
        "appKey":    c.appKey,
        "method":    method,
        "timestamp": getTimestamp(),
    }
    requestParameters := sysParameters
    if bizParams != nil {
        for key, value := range bizParams {
            requestParameters[key] = value
        }
    }

    for name, data := range binaryParams {
        digestName := name + "Md5"
        value := fmt.Sprintf("%X", md5.Sum(data.Data))
        requestParameters[digestName] = value
    }

    signature := sign(requestParameters, c.secret)
    requestParameters["sign"] = signature

    body := &amp;bytes.Buffer{}
    writer := multipart.NewWriter(body)
    for name, value := range requestParameters {
        writer.WriteField(name, value)
    }
    for name, data := range binaryParams {
        part, err := writer.CreateFormFile(name, data.OriginalFileName)
        if err != nil {
            return "", err
        }
        part.Write(data.Data)
    }
    err := writer.Close()
    if err != nil {
        return "", err
    }

    req, err := http.NewRequest("POST", c.url, body)
    if err != nil {
        return "", err
    }
    req.Header.Set("Content-Type", writer.FormDataContentType())

    resp, err := c.client.Do(req)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()

    responseBody, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return "", err
    }

    return string(responseBody), nil
}
</code>
</pre>
</div>
</body>
</html>

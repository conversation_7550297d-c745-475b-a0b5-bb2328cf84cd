<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    API 接口文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    <pre><code>
import hashlib
import http.client
import ssl
import urllib.parse
import datetime
from urllib.parse import urlparse


def _get_timestamp():
    d = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return d


def _sign(params, secret):
    if isinstance(params, dict):
        params = params.copy()
        keys = sorted(params.keys())
        params = "%s;%s;%s" % (secret, "&amp;".join('%s=%s' % (key, params[key]) for key in keys), secret)
    return hashlib.md5(params.encode('utf-8')).hexdigest().upper()


class HttpApiClient:
    def __init__(self, app_key, secret, url, timeout=3):
        self._app_key = app_key
        self._secret = secret
        self._url = url
        self._timeout = timeout

    def execute(self, method, biz_params):
        sys_parameters = {
            "appKey": self._app_key,
            "method": method,
            "timestamp": _get_timestamp()
        }
        request_parameters = sys_parameters.copy()
        if biz_params is not None:
            request_parameters.update(biz_params)
        sign = _sign(request_parameters, self._secret)
        request_parameters["sign"] = sign
        client = _HttpClient(self._timeout)
        return client.do_form_post(self._url, request_parameters)

    def execute_multipart(self, method, biz_params, binary_params):
        sys_parameters = {
            "appKey": self._app_key,
            "method": method,
            "timestamp": _get_timestamp()
        }
        request_parameters = sys_parameters.copy()
        if biz_params is not None:
            request_parameters.update(biz_params)
        for (name, data) in binary_params.items():
            digest_name = name + "Md5"
            value = hashlib.md5(data[1]).hexdigest()
            request_parameters[digest_name] = value

        sign = _sign(request_parameters, self._secret)
        request_parameters["sign"] = sign

        parts = []
        for (name, value) in request_parameters.items():
            parts.append(_Part(name, value, False, None))

        for (name, data) in binary_params.items():
            parts.append(_Part(name, data[1], True, data[0]))

        client = _HttpClient(self._timeout)

        return client.do_multipart_request(self._url, parts)


_BOUNDARY = '-----RP20140102314159265358979'
_CHARSET = 'utf-8'


class _BytesBody:
    def __init__(self):
        self._body = []

    def append_str(self, s):
        self._body.append(s.encode(_CHARSET))

    def append_bin(self, data):
        self._body.append(data)

    def join(self, sep):
        return sep.join(self._body)


class _Part:
    def __init__(self, field_name, value, is_bin, file_name):
        self._field_name = field_name
        self._value = value
        self._is_bin = is_bin
        self._file_name = file_name

    def write_to(self, bytes_body):
        bytes_body.append_str('--' + _BOUNDARY)
        if self._is_bin:
            bytes_body.append_str(
                'Content-Disposition: form-data; name="{}"; filename="{}"'.format(self._field_name, self._file_name))
            bytes_body.append_str('Content-Type: application/octet-stream')
        else:
            bytes_body.append_str('Content-Disposition: form-data; name="{}"'.format(self._field_name))
        bytes_body.append_str('')
        if self._is_bin:
            bytes_body.append_bin(self._value)
        else:
            bytes_body.append_str(self._value)


class _HttpClient:
    def __init__(self, timeout):
        self._timeout = timeout

    def do_multipart_request(self, url, parts):
        headers = {
            "Content-Type": "multipart/form-data; boundary={}".format(_BOUNDARY)
        }
        body = _BytesBody()
        for part in parts:
            part.write_to(body)
        body.append_str('--' + _BOUNDARY + '--')
        body.append_str('')
        body_data = body.join(b'\r\n')
        return self.do_request("POST", url, body_data, headers)

    def do_form_post(self, url, post_data):
        post_header = {
            'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
            "Cache-Control": "no-cache"
        }
        body = urllib.parse.urlencode(post_data)
        return self.do_request("POST", url, body.encode(_CHARSET), post_header)

    def do_request(self, method, url, body, headers):
        parsed_url = urlparse(url)
        if parsed_url.scheme == 'https':
            connection = http.client.HTTPSConnection(parsed_url.hostname, port=parsed_url.port, timeout=self._timeout, context=ssl._create_unverified_context())
        else:
            connection = http.client.HTTPConnection(parsed_url.hostname, port=parsed_url.port, timeout=self._timeout)
        connection.connect()
        connection.request(method, url, body=body, headers=headers)
        response = connection.getresponse()
        if response.status != 200:
            raise Exception(
                'invalid http status ' + str(response.status) + ', detail body: ' + response.read().decode())
        result = response.read().decode()
        connection.close()
        return result
</code></pre>
</div>
</body>
</html>

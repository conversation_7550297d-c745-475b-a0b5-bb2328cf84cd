<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    接口文档 - M01-墨西哥黑名单查询
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    
<h1>接口文档 - M01-墨西哥黑名单查询</h1>


<h2>M01-墨西哥黑名单查询</h2>
<p>mex.black.list.query</p>

<h2>接口说明</h2>
<p>根据手机号码或是CURP查询黑名单</p>



<h2>业务输入参数</h2>
<table class="params-table">
    <tr>
        <th style="width: 10%">参数名</th>
        <th style="width: 50%">描述</th>
        <th style="width: 10%">类型</th>
        <th style="width: 10%">是否必须</th>
        <th style="width: 20%">示例</th>
    </tr>
    
                <tr>
            <td>queryType </td>
            <td>
                查询字段类型:1-curp,2-手机号码
                
                            </td>
            <td>整数</td>
            <td>
                                    必须
                            </td>
            <td>
                                    1
                            </td>
        </tr>
    
                <tr>
            <td>queryKey </td>
            <td>
                查询字段的值:curp或是手机号码(手机号码不含国家代码[0052])，取决于query_type字段
                
                            </td>
            <td>字符串</td>
            <td>
                                    必须
                            </td>
            <td>
                                    LOPJ800101HDFRRN01
                            </td>
        </tr>
    
                <tr>
            <td>dateType </td>
            <td>
                查询最近多少时间的数据：目前仅支持: 0-不限制
                
                            </td>
            <td>整数</td>
            <td>
                                    必须
                            </td>
            <td>
                                    0
                            </td>
        </tr>
                <tr>
            <td>requestId</td>
            <td>
                用于标识每次调用接口的请求id(收费接口必传)，要求是纯ascii字符串，长度不超过48，且不可重复。（常见的方式可以使用UUID）
            </td>
            <td>字符串</td>
            <td>必须</td>
            <td>675c2b57-9938-4546-be43-f282e0d9b3ce</td>
        </tr>
    </table>

<h2>系统输入参数</h2>
<table class="params-table">
    <tr>
        <th>参数名</th>
        <th>描述</th>
        <th>示例</th>
    </tr>
    <tr>
        <td>method</td>
        <td>接口方法名</td>
        <td>mex.black.list.query</td>
    </tr>
    <tr>
        <td>appKey</td>
        <td>给客户分配的应用标识</td>
        <td>test123456</td>
    </tr>
    <tr>
        <td>timestamp</td>
        <td>当前时间，请求时间不能晚于服务器时间10分钟</td>
        <td>2014-10-09 12:03:41</td>
    </tr>
    <tr>
        <td>sign</td>
        <td>请求签名，详情见<a href="basic.htm">接口说明文档</a>的签名规则</td>
        <td>D3BEF8A9A7953A21559F8FA7F5A67491</td>
    </tr>
</table>

<h2>计费说明</h2>
<div>
    本接口计费规则为：
    <em>
        查得收费
    </em>
    ，具体价格请参考合同或是联系商务代表。
</div>
<div>
    本接口重复调用计费规则：<em>每次查得请求都会重复计费</em>
</div>

<h2>输出结果</h2>
<pre class="code"><code>{
  &quot;code&quot;: &quot;错误代码 | 0 - 成功，其他值失败，具体请参考文档后面的错误代码&quot;, 
  &quot;message&quot;: &quot;错误信息 | 执行错误时候的提示&quot;, 
  &quot;responseId&quot;: &quot;响应id | 调用收费接口每次请求的标识&quot;, 
  &quot;dataGot&quot;: &quot;是否查得 | 按查得收费的接口，如果该字段返回true，则收费，否则不收费&quot;, 
  &#x2F;&#x2F; 黑名单查询结果
  &quot;data&quot;: {
    &quot;overdueDay&quot;: &quot;逾期天数 | 逾期天数如果超过90天，会返回90&quot;, 
    &quot;loan&quot;: &quot;逾期金额 | 墨西哥比索，单位：元&quot;
  }
}</code></pre>
<div>
    <em>
        说明：有些可为空的字段，如果返回结果为空，默认情况下该字段将不会返回；
        如果想返回这些空的字段，可以在请求参数中加入 withNullField=true
    </em>
</div>



<h2>HTTP描述示例</h2>
<pre class="code"><code>以下仅为请求字段描述，实际请求中请按Http协议中Content-Type为application/x-www-form-urlencoded的规范发送
POST https://api.poysolutions.com/router/rest.htm
Request Header: {"Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"}
Payload:
  method=mex.black.list.query
  appKey=test123456
  timestamp=2014-10-09 12:03:41
  sign=D3BEF8A9A7953A21559F8FA7F5A67491
  queryType=1
  queryKey=LOPJ800101HDFRRN01
  dateType=0
  requestId=675c2b57-9938-4546-be43-f282e0d9b3ce
</code></pre>


<h2>代码调用示例</h2>
<div class="tab">
    <button class="tab_links active" onclick="openTab(event, 'java_code')">Java</button>
    <button class="tab_links" onclick="openTab(event, 'python_code')">Python</button>
    <button class="tab_links" onclick="openTab(event, 'php_code')">PHP</button>
    <button class="tab_links" onclick="openTab(event, 'cs_code')">C#</button>
    <button class="tab_links" onclick="openTab(event, 'go_code')">Go</button>
</div>


<div class="tab_content" style="display: block;" id="java_code">
    <h3>Java</h3>
    <pre  class="code"><code>

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Sample {

    public static void main(String[] args) throws IOException {
        // appKey 和 secret 请联系商务代表获取
        String appKey = "test123456";
        String secret = "......";
        String serverUrl = "https://api.poysolutions.com/router/rest.htm";
        // 根据客户端自己的业务生成或保存，可用于对账
        String requestId = UUID.randomUUID().toString();
        
        // HttpApiClient的源代码: <a href="client/javaClient.htm">HttpApiClient.java</a>
        HttpApiClient httpApiClient = new HttpApiClient(appKey, secret, serverUrl);

        Map&lt;String, String&gt; params = new HashMap&lt;String, String&gt;();
        params.put("queryType", "1");
        params.put("queryKey", "LOPJ800101HDFRRN01");
        params.put("dateType", "0");
        params.put("requestId", requestId);
        
        String result = httpApiClient.execute("mex.black.list.query", params);
        System.out.println(result);

    }
}

</code></pre>

    <a href="client/javaClient.htm">HttpApiClient.java 源代码</a>
</div>


<div class="tab_content" id="python_code">
    <h3>Python</h3>
    <pre  class="code"><code>
import uuid
if __name__ == "__main__":
    # appKey 和 secret 请联系商务代表获取
    appKey = "test123456"
    secret = "......"
    url = "https://api.poysolutions.com/router/rest.htm"
    # 查看 <a href="client/pythonClient.htm">HttpApiClient.py 源代码</a>
    client = HttpApiClient(appKey, secret, url)
    params = {}
    params["queryType"] = "1"
    params["queryKey"] = "LOPJ800101HDFRRN01"
    params["dateType"] = "0"
    # 根据客户端自己的业务生成或保存，可用于对账
    requestId = str(uuid.uuid4())
    params["requestId"] = requestId
    r = client.execute("mex.black.list.query", params)
    print(r)

</code></pre>

    <a href="client/pythonClient.htm">HttpApiClient.py 源代码</a>
</div>



<div class="tab_content" id="php_code">
    <h3>PHP</h3>
    <pre  class="code"><code>
        // appKey 和 secret 请联系商务代表获取
        $appkey = "test123456";
        $secret = "......";
        $url = "https://api.poysolutions.com/router/rest.htm";
        // 查看 <a href="client/phpClient.htm">HttpApiClient.php 源代码</a>
        $client = new HttpApiClient($appkey, $secret, $url);
        $request_params = array();
        $request_params["queryType"] = "1";
        $request_params["queryKey"] = "LOPJ800101HDFRRN01";
        $request_params["dateType"] = "0";
        // 根据客户端自己的业务生成或保存，推荐使用uuid，可用于对账
        $requestId = '......';
        $request_params["requestId"] = $requestId;
    
        echo $client-&gt;execute("mex.black.list.query", $request_params);


</code></pre>

    <a href="client/phpClient.htm">HttpApiClient.php 源代码</a>
</div>



<div class="tab_content" id="cs_code">
    <h3>C#</h3>
    <pre  class="code"><code>
using System;
using System.Collections.Generic;

namespace HttpApiClient
{
    public class Sample
    {
        public static void Main(string[] args)
        {
            var appKey = "test123456";
            var secret = "......";
            var url = "https://api.poysolutions.com/router/rest.htm";

            // 查看 <a href="client/csClient.htm">HttpApiClient.cs 源代码</a>
            var httpClient = new HttpApiClient(appKey, secret, url);

            var bizParams = new Dictionary&lt;string, string&gt;();
            bizParams.Add("queryType", "1");
            bizParams.Add("queryKey", "LOPJ800101HDFRRN01");
            bizParams.Add("dateType", "0");
            // 根据客户端自己的业务生成或保存，可用于对账
            var requestId = Guid.NewGuid().ToString();
            bizParams.Add("requestId", requestId);

            try
            {
                var result = httpClient.Execute("mex.black.list.query", bizParams);
                Console.WriteLine(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine("An error occurred: " + ex.Message);
            }

        }
    }
}
</code></pre>

    <a href="client/csClient.htm">HttpApiClient.cs 源代码</a>
</div>


<div class="tab_content" id="go_code">
    <h3>Go</h3>
    <pre  class="code"><code>
package main

import "fmt"

func main() {
	appKey := "test123456"
	secret := "......"
	url := "https://api.poysolutions.com/router/rest.htm"
	timeout := 3000
        // 查看 <a href="client/goClient.htm">client.go 源代码</a>
	client := NewHttpApiClient(appKey, secret, url, timeout)
	bizParams := make(map[string]string)
        bizParams["queryType"] = "1"
        bizParams["queryKey"] = "LOPJ800101HDFRRN01"
        bizParams["dateType"] = "0"
        // 根据客户端自己的业务生成或保存，可用于对账
        requestId := "your_uuid"
        bizParams["requestId"] = requestId
        result, err := client.Execute("mex.black.list.query", bizParams)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	fmt.Println(result)
}

</code></pre>

    <a href="client/goClient.htm">client.go 源代码</a>
</div>




<h2>错误代码</h2>

<table class="error-codes-table">
    <tr>
        <th>code</th>
        <th>说明</th>
        <th>解决建议</th>
    </tr>
            <tr>
            <td>0</td>
            <td>成功</td>
            <td>无</td>
        </tr>
            <tr>
            <td>100</td>
            <td>无效appKey</td>
            <td>请检查appKey是否正确</td>
        </tr>
            <tr>
            <td>101</td>
            <td>appKey已过期</td>
            <td>请联系商务代表</td>
        </tr>
            <tr>
            <td>200</td>
            <td>签名错误</td>
            <td>请检查签名规则或使用sdk请求</td>
        </tr>
            <tr>
            <td>301</td>
            <td>非法参数</td>
            <td>请检查参数和参考具体响应提示</td>
        </tr>
            <tr>
            <td>302</td>
            <td>时间戳过期</td>
            <td>请检查时间戳参数</td>
        </tr>
            <tr>
            <td>401</td>
            <td>账户余额不足</td>
            <td>请联系商务代表</td>
        </tr>
            <tr>
            <td>500</td>
            <td>服务器内部错误</td>
            <td>请联系客服</td>
        </tr>
            <tr>
            <td>600</td>
            <td>api不存在</td>
            <td>请检查方法名是否正确</td>
        </tr>
            <tr>
            <td>601</td>
            <td>没有权限</td>
            <td>接口未授权，请联系商务代表</td>
        </tr>
            <tr>
            <td>901</td>
            <td>每秒请求超过上限</td>
            <td>请联系商务代表增加上限</td>
        </tr>
            <tr>
            <td>902</td>
            <td>每天请求超过上限</td>
            <td>请联系商务代表增加上限</td>
        </tr>
            <tr>
            <td>10001</td>
            <td>无效的参数</td>
            <td>调整参数</td>
        </tr>
    </table>

<script>
    function openTab(evt, tabName) {
        // 获取所有 tab_content 元素并隐藏它们
        let tab_content = document.getElementsByClassName("tab_content");
        for (let i = 0; i < tab_content.length; i++) {
            tab_content[i].style.display = "none";
        }

        // 获取所有 tab_links 元素并移除 active 类名
        let tab_links = document.getElementsByClassName("tab_links");
        for (let i = 0; i < tab_links.length; i++) {
            tab_links[i].className = tab_links[i].className.replace(" active", "");
        }

        // 显示当前选中的 tab_content 元素并添加 active 类名到对应的 tab_link 元素
        document.getElementById(tabName).style.display = "block";
        evt.currentTarget.className += " active";
    }
</script>
</div>
</body>
</html>

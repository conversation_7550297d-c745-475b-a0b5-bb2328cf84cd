<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    接口说明文档
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    

<h2>接口说明文档</h2>

<p class="section">
<h4>接口概述</h4>
<div>
    接口基于标准的http协议，按接口协议组装成一个的http请求，通过该http请求到服务端，获取响应，完成一次api调用。
</div>
</p>
<p class="section">
<h4>调用参数</h4>
<div>
    调用api ，必须传入系统参数和应用参数。系统参数详细介绍如下；应用参数由于不同 api 各自不同，这里以
    myfoo.name.get为例说明，更多信息参考各个api说明。
    <h5>系统参数</h5>
    <table>
        <tr>
            <td>名称</td>
            <td>类型</td>
            <td>是否必须</td>
            <td>描述</td>
        </tr>
        <tr>
            <td>method</td>
            <td>string</td>
            <td>是</td>
            <td>api方法标识</td>
        </tr>
        <tr>
            <td>timestamp</td>
            <td>string</td>
            <td>是</td>
            <td>时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2014-05-29 18:52:03。服务端允许客户端请求时间误差为10分钟。</td>
        </tr>
        <tr>
            <td>sign</td>
            <td>string</td>
            <td>是</td>
            <td>对 API 输入参数进行 md5 加密获得，详细参考<i>签名sign</i></td>
        </tr>
    </table>
    <h5>应用参数</h5>
    <div>
        根据具体业务接口而定，以下有几个通用情况：
        <ul>
            <li>如果是付费接口，需要额外入参requestId字段，客户侧要求唯一，用于以后的对账，可以使用UUID。</li>
            <li>如果某字段是二进制类型，则该字段本身不进入签名计算，但是需要提供额外的md5摘要入参字段，内容为该二进制的md5摘要（十六进制表示，大小写均可），
                摘要入参字段名称是在其名称后加Md5（比如：
                二进制的字段名称是myFile, 则其摘要字段是myFileMd5），该字段参与签名计算。</li>
        </ul>
    </div>

    <h4 name="sign">签名sign</h4>
    <pre>调用API 时需要对请求参数进行签名验证，服务器也会对该请求参数进行验证是否合法的。方法如下：
根据参数名称（除签名外）将所有请求参数按照字母先后顺序排序:key=value&amp;key=value&amp; .... &amp;key=value
例如：将foo=1,bar=2,baz=3 排序为bar=2,baz=3,foo=1，参数名和参数值用=连接后， 再用&把键值对拼装成字符串bar=2&amp;baz=3&amp;foo=1
然后将secret拼接到参数字符串头和尾，中间用;连接，进行md5加密，用十六进制表示，再转化成大写，签名格式描述：upperCase(byte2hex(md5(secret;key1=value1&amp;key2=value2...;secret))
其中，appKey，secret由平台分配。</pre>
</div>
</p>

<p class="section">
<h4>调用示例</h4>
<div>
    <div>
        调用API: myfoo.name.get，使用MD5加密，因为各语言语法不一致，以下实例只体现逻辑。
        为便于说明，假设 appKey为testKey、secret为testSecret。
    </div>
    <h5>1,输入参数如下</h5>
    <pre>method=myfoo.name.get
timestamp=2014-05-29 18:52:03
appKey=testKey
requestId=xxyyaabbcczzff</pre>
    <h5>2,按照参数名称升序排列</h5>
    <pre>appKey=testKey
method=myfoo.name.get
requestId=xxyyaabbcczzff
timestamp=2014-05-29 18:52:03</pre>
    <h5>3,连接字符串</h5>
    <div>
        连接参数名与参数值,并在首尾加上secret，如下：
    </div>
    <pre>testSecret;appKey=testKey&amp;method=myfoo.name.get&amp;requestId=xxyyaabbcczzff&amp;timestamp=2014-05-29 18:52:03;testSecret</pre>
    <h5>4,生成签名</h5>
    <div>
        进行MD5加密，用十六进制位大写表示 -> C9BEF8A9A7953A31559F8FA7C5A67493
    </div>
    <h5>5,组装成http请求</h5>
    <div>
        以GET请求为例，将所有参数值转换为UTF-8编码，然后拼装，通过浏览器访问该地址，即成功调用一次接口，如下：
    </div>
    <div>
        用GET请求或POST请求皆可，请求内容为：
        <pre>sign=C9BEF8A9A7953A31559F8FA7C5A67493&amp;timestamp=2014-05-29 18:52:03&amp;appKey=testKey&amp;requestId=xxyyaabbcczzff&amp;method=myfoo.name.get</pre>
    </div>
</div>
</p>

<p class="section">
<h4>注意事项</h4>
<div>
    1, 所有的请求和响应数据编码皆为utf-8格式；
    请求时，Content-Type只支持两种类型，即：application/x-www-form-urlencoded;charset=UTF-8和multipart/form-data。
</div>
<div>
    2, 如果请求的Content-Type是application/x-www-form-urlencoded;charset=UTF-8，http body里的所有参数值建议做好urlencode编码。
    （签名计算前不要做，不然签名就不一致了；在发送请求前处理。）
</div>
<div>
    3, 如果请求的Content-Type是multipart/form-data格式，二进制字段不参与签名计算，但其对应的Md5摘要字段会参与签名计算，另外每个表单字段的参数值无需urlencode编码,但每个表单字段的charset部分需要指定为utf-8，
</div>
<div>
    4, 建议所有接口都使用POST method请求。
</div>
<div>
    5, 强烈推荐使用平台封装好的工具类进行接口调用。
</div>
</p>





</div>
</body>
</html>

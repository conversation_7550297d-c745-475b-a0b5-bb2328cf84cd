<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
                    接口文档 - M15-墨西哥用户标签查询
            </title>
    <style>
        body {
            font-family:  Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        #container {
            width: 1280px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #666;
            font-size: 24px;
            margin-top: 40px;
        }

        p {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .params-table th:nth-child(1) {
            width: 10%;
        }

        .params-table th:nth-child(2) {
            width: 55%;
        }

        .params-table th:nth-child(3) {
            width: 25%;
        }

        .params-table em {
            color: #5e5e5e;
        }

        .error-codes-table th:nth-child(1) {
            width: 15%;
        }

        .error-codes-table th:nth-child(2) {
            width: 30%;
        }

        .error-codes-table th:nth-child(3) {
            width: 55%;
        }

        code {
            padding: 2px 4px;
            border-radius: 4px;
            font-family: Consolas, monospace;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-left: 4px solid #ccc;
            overflow-x: auto;
        }

        .param-desc {
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Tab页样式 */
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f5f5f5;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: background-color 0.3s ease;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tab_content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
        }


    </style>
</head>
<body>
<div id="container">
    
<h1>接口文档 - M15-墨西哥用户标签查询</h1>


<h2>M15-墨西哥用户标签查询</h2>
<p>mex.user.tag.query</p>

<h2>接口说明</h2>
<p>墨西哥用户标签查询</p>



<h2>业务输入参数</h2>
<table class="params-table">
    <tr>
        <th style="width: 10%">参数名</th>
        <th style="width: 50%">描述</th>
        <th style="width: 10%">类型</th>
        <th style="width: 10%">是否必须</th>
        <th style="width: 20%">示例</th>
    </tr>
    
                <tr>
            <td>keywords </td>
            <td>
                标签查询输入：可以是手机号,邮箱,18位CURP的组合，多个输入用英文逗号拼接（中间不要有多余空格）。关键字数量为1-3个。
                
                            </td>
            <td>字符串</td>
            <td>
                                    必须
                            </td>
            <td>
                                    8111053281,AAAA000112MNLLNNB8
                            </td>
        </tr>
                <tr>
            <td>requestId</td>
            <td>
                用于标识每次调用接口的请求id(收费接口必传)，要求是纯ascii字符串，长度不超过48，且不可重复。（常见的方式可以使用UUID）
            </td>
            <td>字符串</td>
            <td>必须</td>
            <td>675c2b57-9938-4546-be43-f282e0d9b3ce</td>
        </tr>
    </table>

<h2>系统输入参数</h2>
<table class="params-table">
    <tr>
        <th>参数名</th>
        <th>描述</th>
        <th>示例</th>
    </tr>
    <tr>
        <td>method</td>
        <td>接口方法名</td>
        <td>mex.user.tag.query</td>
    </tr>
    <tr>
        <td>appKey</td>
        <td>给客户分配的应用标识</td>
        <td>test123456</td>
    </tr>
    <tr>
        <td>timestamp</td>
        <td>当前时间，请求时间不能晚于服务器时间10分钟</td>
        <td>2014-10-09 12:03:41</td>
    </tr>
    <tr>
        <td>sign</td>
        <td>请求签名，详情见<a href="basic.htm">接口说明文档</a>的签名规则</td>
        <td>D3BEF8A9A7953A21559F8FA7F5A67491</td>
    </tr>
</table>

<h2>计费说明</h2>
<div>
    本接口计费规则为：
    <em>
        查得收费
    </em>
    ，具体价格请参考合同或是联系商务代表。
</div>
<div>
    本接口重复调用计费规则：<em>每次查得请求都会重复计费</em>
</div>

<h2>输出结果</h2>
<pre class="code"><code>{
  &quot;code&quot;: &quot;错误代码 | 0 - 成功，其他值失败，具体请参考文档后面的错误代码&quot;, 
  &quot;message&quot;: &quot;错误信息 | 执行错误时候的提示&quot;, 
  &quot;responseId&quot;: &quot;响应id | 调用收费接口每次请求的标识&quot;, 
  &quot;dataGot&quot;: &quot;是否查得 | 按查得收费的接口，如果该字段返回true，则收费，否则不收费&quot;, 
  &#x2F;&#x2F; 标签结果数据
  &quot;data&quot;: {
    &quot;employmentType&quot;: &quot;就业类型 | 1-7，7档就业类型⽔平，取值越⾼则⽤户的⼯作类型越不稳定（全职→兼职→失业）&quot;, 
    &quot;education&quot;: &quot;教育背景 | 1-5，5档教育背景⽔平，取值越⾼则⽤户的受教育⽔平越低&quot;, 
    &quot;salaryRange&quot;: &quot;薪资范围 | 1-21，21档薪资范围，取值越⾼则⽤户的薪资越⾼&quot;, 
    &quot;salaryTimes&quot;: &quot;发薪频率 | 1-8，8档发薪频率，取值越⾼则⽤户的发薪频率越低&quot;, 
    &quot;registerBankCnt&quot;: &quot;注册银⾏机构数量 | 所查询⽤户的注册银⾏机构数量&quot;, 
    &#x2F;&#x2F; 注册银⾏机构信息 | 所查询⽤户的注册银⾏机构信息
    &quot;registerBanks&quot;: [
      &quot;注册银⾏机构信息&quot;
    ], 
    &quot;imssAddressCode&quot;: &quot;社保缴纳归属地 | 1:Aguascalientes, 2:Baja California, 3:Baja CaliforniaSur, 4:Campeche, 5:Coahuila de Zaragoza, 6:Colima, 7:Chiapas, 8:Chihuahua, 9:Ciudad de Mexico, 10:Durango, 11:Guanajuato, 12:Guerrero, 13:Hidalgo, 14:Jalisco, 15:Estado de Mexico, 16:Michoacan deOcampo, 17:Morelos, 18:Nayarit, 19:Nuevo Leon, 20:Oaxaca, 21:Puebla, 22:Queretaro, 23:Quintana Roo, 24:San Luis Potosi, 25:Sinaloa, 26:Sonora, 27:Tabasco, 28:Tam aulipas, 29:Tlaxcala, 30:Veracruz de Ignacio de la Llave, 31:Yucatan, 32:Zacatecas&quot;, 
    &quot;lastMonthImssCnt&quot;: &quot;上⽉缴纳社保机构数量 | 所查询⽤户的上⽉缴纳社保机构数量&quot;, 
    &quot;imssBaseSalary&quot;: &quot;社保局记录⽇薪（等级分） | 0-100，得分越⾼则⽤户的社保局记录⽇薪越⾼&quot;, 
    &quot;imssContribDays&quot;: &quot;上⽉⼯作天数: | 所查询⽤户的上⽉⼯作天数&quot;, 
    &quot;bankLevelLabel&quot;: &quot;银⾏等级 | 1:普通，2:中端，3:⾼端&quot;, 
    &quot;bankDeposit_1y&quot;: &quot;近1年银⾏存款额（等级分） | 0～100，得分越⾼则⽤户的银⾏存款额越⾼&quot;, 
    &quot;bankDeposit_3y&quot;: &quot;近3年银⾏存款额（等级分） | 0～100，得分越⾼则⽤户的银⾏存款额越⾼&quot;, 
    &quot;assetLabel&quot;: &quot;资产⽔平 | 1:低资产，2:中等资产，3:⾼资产&quot;, 
    &quot;occupation&quot;: &quot;职业 | 1:失业, 2:退休, 3:学⽣, 4:家庭主妇, 5:临时⼯,6:⼯⼚⼯⼈, 7:公司职员, 8: ⾃由职业&#x2F;个体户, 9:公务员,10:专业技术⼈员, 11:雇主&#x2F;管理⼈员&#x2F;企业家, 12:其他&quot;, 
    &quot;sourceIncome&quot;: &quot;收⼊来源 | 1-7，7档收⼊来源⽔平，取值越⾼则⽤户的经济来源越独⽴&quot;, 
    &quot;maritalStatus&quot;: &quot;婚姻状态 | 1:已婚，2:单身，3:离婚，4:丧偶&quot;, 
    &quot;residenceType&quot;: &quot;居住类型 | 1:有⼀套以上房产，2:有房产⾃住，3:租房，4:与家⼈同住，5:⽆房产&quot;, 
    &quot;childStatus&quot;: &quot;⼦⼥状态 | 1-5，5档⼦⼥状态⽔平，取值越⾼则⽤户的⼦⼥数量越少&quot;, 
    &quot;serviceLife&quot;: &quot;⼯作合同服务年限 | 1-10，10档⼯作合同服务年限，取值越⾼则⽤户与当前雇主的⼯作合同年限越短&quot;, 
    &quot;cityRankLabel&quot;: &quot;⼈群城市 | 1:三线城市，2:⼆线城市，3:⼀线城市&quot;, 
    &quot;interestLabel&quot;: &quot;兴趣⼼理偏好 | 1:健康⽣活者，2:科技爱好者&quot;, 
    &quot;bankRecordSalary&quot;: &quot;银⾏记录收⼊（等级分） | 0-100，得分越⾼则⽤户的银⾏记录收⼊越⾼&quot;, 
    &quot;loyaltyLabel&quot;: &quot;顾客忠诚度 | 1:低忠诚度，2:中等忠诚度，3:⾼忠诚度&quot;, 
    &quot;teleConsumLabel&quot;: &quot;电信套餐消费 | 1:低消费，2:中等消费，3:⾼消费&quot;, 
    &quot;teleServiceLevel&quot;: &quot;电信公司服务等级 | 1:⼀级，2:⼆级，3:三级&quot;, 
    &quot;bankCreditLimit&quot;: &quot;银⾏信⽤额度（等级分） | 0-100，得分越⾼则⽤户的银⾏信⽤额度越⾼&quot;, 
    &quot;registerMultiLoan&quot;: &quot;注册三⽅借贷平台数量 | 所查询⽤户的注册三⽅借贷平台数量&quot;, 
    &quot;baseRiskLevel&quot;: &quot;信贷app历史基础⻛险分（等级分） | 0-100，得分越⾼则⽤户发⽣信贷违约⾏为的概率越⾼&quot;, 
    &quot;withdrawMoney&quot;: &quot;信贷app历史可提现⾦额（等级分） | 0-100，得分越⾼则⽤户的信贷app历史可提现⾦额越⾼&quot;, 
    &quot;preferredRegisterWay&quot;: &quot;⽤户注册⽅式偏好 | 1:⼿机短信，2:whatsapp&quot;, 
    &quot;timesOfReBorrowing&quot;: &quot;历史复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_7d&quot;: &quot;历史⾸借后7天复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_15d&quot;: &quot;历史⾸借后15天复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_30d&quot;: &quot;历史⾸借后30天复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_90d&quot;: &quot;历史⾸借后90天复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_180d&quot;: &quot;历史⾸借后180天复借次数（等级分） | 0-100，得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;reBorrowing_365d&quot;: &quot;历史⾸借后365天复借次数（等级分） | 0-100， 得分越⾼则⽤户的历史复借次数越多&quot;, 
    &quot;applyLoanCnt&quot;: &quot;历史申请贷款数（等级分） | 0-10，得分越⾼则⽤户的历史申请贷款数越多&quot;, 
    &quot;nightApplyLoanCnt&quot;: &quot;历史夜间申请贷款数（等级分） | 0-10，得分越⾼则⽤户的历史夜间申请贷款数越多&quot;, 
    &quot;weekendApplyLoanCnt&quot;: &quot;历史周末申请贷款数（等级分） | 0-10，得分越⾼则⽤户的历史周末申请贷款数越多&quot;, 
    &quot;maxMonthApplyCnt&quot;: &quot;历史最⼤⽉申请次数（等级分） | 0-10，得分越⾼则⽤户的历史最⼤⽉申请次数越多&quot;, 
    &quot;minMonthApplyCnt&quot;: &quot;历史最⼩⽉申请次数（等级分） | 0-10，得分越⾼则⽤户的历史最⼩⽉申请次数越多&quot;, 
    &quot;applyMonthCnt&quot;: &quot;历史有申请记录的⽉份数（等级分） | 0-10，得分越⾼则⽤户历史有申请记录的⽉份数越多&quot;, 
    &quot;applyCntPerMonth&quot;: &quot;历史平均每⽉申请次数（等级分） | 0-10，得分越⾼则⽤户的历史平均每⽉申请次数越多&quot;, 
    &quot;validApplyCnt&quot;: &quot;历史成功申贷订单计数（等级分） | 0-10，得分越⾼则⽤户的历史成功申贷订单数量越多&quot;, 
    &quot;loanAmount&quot;: &quot;历史成功申贷⾦额总和（等级分） | 0-100，得分越⾼则⽤户的历史成功申贷⾦额越⾼&quot;, 
    &quot;maxApplyGapDay&quot;: &quot;历史申请最⼤间隔天数（等级分） | 0-100，得分越⾼则⽤户的历史申请最⼤间隔天数越⻓&quot;, 
    &quot;unpaiedCnt&quot;: &quot;历史⽋费订单计数（等级分） | 0-10，得分越⾼则⽤户的历史⽋费订单数量越多&quot;, 
    &quot;unpaiedAmount&quot;: &quot;历史⽋费⾦额总和（等级分） | 0-100，得分越⾼则⽤户的历史⽋费⾦额越⾼&quot;, 
    &quot;finishedCnt&quot;: &quot;历史还款订单计数（等级分） | 0-10，得分越⾼则⽤户的历史还款订单数量越多&quot;, 
    &quot;finishedAmount&quot;: &quot;历史还款⾦额总和（等级分） | 0-100，得分越⾼则⽤户的历史还款⾦额越⾼&quot;, 
    &quot;finishedRatio&quot;: &quot;历史还款率（等级分） | 0-1，得分越⾼则⽤户的历史还款率越⾼&quot;, 
    &quot;fpd1Cnt&quot;: &quot;历史逾期1天订单数（等级分） | 0-10，得分越⾼则⽤户的历史逾期订单数越多&quot;, 
    &quot;fpd3Cnt&quot;: &quot;历史逾期3天订单数（等级分） | 0-10，得分越⾼则⽤户的历史逾期订单数越多&quot;, 
    &quot;fpd5Cnt&quot;: &quot;历史逾期5天订单数（等级分） | 0-10，得分越⾼则⽤户的历史逾期订单数越多&quot;, 
    &quot;fpd7Cnt&quot;: &quot;历史逾期7天订单数（等级分） | 0-10，得分越⾼则⽤户的历史逾期订单数越多&quot;, 
    &quot;fpd30Cnt&quot;: &quot;历史逾期30天订单数（等级分） | 0-10，得分越⾼则⽤户的历史逾期订单数越多&quot;, 
    &quot;maxOverdueDays&quot;: &quot;历史最⼤逾期天数（等级分） | 0-100，得分越⾼则⽤户的历史最⼤逾期天数越⻓&quot;
  }
}</code></pre>
<div>
    <em>
        说明：有些可为空的字段，如果返回结果为空，默认情况下该字段将不会返回；
        如果想返回这些空的字段，可以在请求参数中加入 withNullField=true
    </em>
</div>



<h2>HTTP描述示例</h2>
<pre class="code"><code>以下仅为请求字段描述，实际请求中请按Http协议中Content-Type为application/x-www-form-urlencoded的规范发送
POST https://api.poysolutions.com/router/rest.htm
Request Header: {"Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"}
Payload:
  method=mex.user.tag.query
  appKey=test123456
  timestamp=2014-10-09 12:03:41
  sign=D3BEF8A9A7953A21559F8FA7F5A67491
  keywords=8111053281,AAAA000112MNLLNNB8
  requestId=675c2b57-9938-4546-be43-f282e0d9b3ce
</code></pre>


<h2>代码调用示例</h2>
<div class="tab">
    <button class="tab_links active" onclick="openTab(event, 'java_code')">Java</button>
    <button class="tab_links" onclick="openTab(event, 'python_code')">Python</button>
    <button class="tab_links" onclick="openTab(event, 'php_code')">PHP</button>
    <button class="tab_links" onclick="openTab(event, 'cs_code')">C#</button>
    <button class="tab_links" onclick="openTab(event, 'go_code')">Go</button>
</div>


<div class="tab_content" style="display: block;" id="java_code">
    <h3>Java</h3>
    <pre  class="code"><code>

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Sample {

    public static void main(String[] args) throws IOException {
        // appKey 和 secret 请联系商务代表获取
        String appKey = "test123456";
        String secret = "......";
        String serverUrl = "https://api.poysolutions.com/router/rest.htm";
        // 根据客户端自己的业务生成或保存，可用于对账
        String requestId = UUID.randomUUID().toString();
        
        // HttpApiClient的源代码: <a href="client/javaClient.htm">HttpApiClient.java</a>
        HttpApiClient httpApiClient = new HttpApiClient(appKey, secret, serverUrl);

        Map&lt;String, String&gt; params = new HashMap&lt;String, String&gt;();
        params.put("keywords", "8111053281,AAAA000112MNLLNNB8");
        params.put("requestId", requestId);
        
        String result = httpApiClient.execute("mex.user.tag.query", params);
        System.out.println(result);

    }
}

</code></pre>

    <a href="client/javaClient.htm">HttpApiClient.java 源代码</a>
</div>


<div class="tab_content" id="python_code">
    <h3>Python</h3>
    <pre  class="code"><code>
import uuid
if __name__ == "__main__":
    # appKey 和 secret 请联系商务代表获取
    appKey = "test123456"
    secret = "......"
    url = "https://api.poysolutions.com/router/rest.htm"
    # 查看 <a href="client/pythonClient.htm">HttpApiClient.py 源代码</a>
    client = HttpApiClient(appKey, secret, url)
    params = {}
    params["keywords"] = "8111053281,AAAA000112MNLLNNB8"
    # 根据客户端自己的业务生成或保存，可用于对账
    requestId = str(uuid.uuid4())
    params["requestId"] = requestId
    r = client.execute("mex.user.tag.query", params)
    print(r)

</code></pre>

    <a href="client/pythonClient.htm">HttpApiClient.py 源代码</a>
</div>



<div class="tab_content" id="php_code">
    <h3>PHP</h3>
    <pre  class="code"><code>
        // appKey 和 secret 请联系商务代表获取
        $appkey = "test123456";
        $secret = "......";
        $url = "https://api.poysolutions.com/router/rest.htm";
        // 查看 <a href="client/phpClient.htm">HttpApiClient.php 源代码</a>
        $client = new HttpApiClient($appkey, $secret, $url);
        $request_params = array();
        $request_params["keywords"] = "8111053281,AAAA000112MNLLNNB8";
        // 根据客户端自己的业务生成或保存，推荐使用uuid，可用于对账
        $requestId = '......';
        $request_params["requestId"] = $requestId;
    
        echo $client-&gt;execute("mex.user.tag.query", $request_params);


</code></pre>

    <a href="client/phpClient.htm">HttpApiClient.php 源代码</a>
</div>



<div class="tab_content" id="cs_code">
    <h3>C#</h3>
    <pre  class="code"><code>
using System;
using System.Collections.Generic;

namespace HttpApiClient
{
    public class Sample
    {
        public static void Main(string[] args)
        {
            var appKey = "test123456";
            var secret = "......";
            var url = "https://api.poysolutions.com/router/rest.htm";

            // 查看 <a href="client/csClient.htm">HttpApiClient.cs 源代码</a>
            var httpClient = new HttpApiClient(appKey, secret, url);

            var bizParams = new Dictionary&lt;string, string&gt;();
            bizParams.Add("keywords", "8111053281,AAAA000112MNLLNNB8");
            // 根据客户端自己的业务生成或保存，可用于对账
            var requestId = Guid.NewGuid().ToString();
            bizParams.Add("requestId", requestId);

            try
            {
                var result = httpClient.Execute("mex.user.tag.query", bizParams);
                Console.WriteLine(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine("An error occurred: " + ex.Message);
            }

        }
    }
}
</code></pre>

    <a href="client/csClient.htm">HttpApiClient.cs 源代码</a>
</div>


<div class="tab_content" id="go_code">
    <h3>Go</h3>
    <pre  class="code"><code>
package main

import "fmt"

func main() {
	appKey := "test123456"
	secret := "......"
	url := "https://api.poysolutions.com/router/rest.htm"
	timeout := 3000
        // 查看 <a href="client/goClient.htm">client.go 源代码</a>
	client := NewHttpApiClient(appKey, secret, url, timeout)
	bizParams := make(map[string]string)
        bizParams["keywords"] = "8111053281,AAAA000112MNLLNNB8"
        // 根据客户端自己的业务生成或保存，可用于对账
        requestId := "your_uuid"
        bizParams["requestId"] = requestId
        result, err := client.Execute("mex.user.tag.query", bizParams)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	fmt.Println(result)
}

</code></pre>

    <a href="client/goClient.htm">client.go 源代码</a>
</div>




<h2>错误代码</h2>

<table class="error-codes-table">
    <tr>
        <th>code</th>
        <th>说明</th>
        <th>解决建议</th>
    </tr>
            <tr>
            <td>0</td>
            <td>成功</td>
            <td>无</td>
        </tr>
            <tr>
            <td>100</td>
            <td>无效appKey</td>
            <td>请检查appKey是否正确</td>
        </tr>
            <tr>
            <td>101</td>
            <td>appKey已过期</td>
            <td>请联系商务代表</td>
        </tr>
            <tr>
            <td>200</td>
            <td>签名错误</td>
            <td>请检查签名规则或使用sdk请求</td>
        </tr>
            <tr>
            <td>301</td>
            <td>非法参数</td>
            <td>请检查参数和参考具体响应提示</td>
        </tr>
            <tr>
            <td>302</td>
            <td>时间戳过期</td>
            <td>请检查时间戳参数</td>
        </tr>
            <tr>
            <td>401</td>
            <td>账户余额不足</td>
            <td>请联系商务代表</td>
        </tr>
            <tr>
            <td>500</td>
            <td>服务器内部错误</td>
            <td>请联系客服</td>
        </tr>
            <tr>
            <td>600</td>
            <td>api不存在</td>
            <td>请检查方法名是否正确</td>
        </tr>
            <tr>
            <td>601</td>
            <td>没有权限</td>
            <td>接口未授权，请联系商务代表</td>
        </tr>
            <tr>
            <td>901</td>
            <td>每秒请求超过上限</td>
            <td>请联系商务代表增加上限</td>
        </tr>
            <tr>
            <td>902</td>
            <td>每天请求超过上限</td>
            <td>请联系商务代表增加上限</td>
        </tr>
    </table>

<script>
    function openTab(evt, tabName) {
        // 获取所有 tab_content 元素并隐藏它们
        let tab_content = document.getElementsByClassName("tab_content");
        for (let i = 0; i < tab_content.length; i++) {
            tab_content[i].style.display = "none";
        }

        // 获取所有 tab_links 元素并移除 active 类名
        let tab_links = document.getElementsByClassName("tab_links");
        for (let i = 0; i < tab_links.length; i++) {
            tab_links[i].className = tab_links[i].className.replace(" active", "");
        }

        // 显示当前选中的 tab_content 元素并添加 active 类名到对应的 tab_link 元素
        document.getElementById(tabName).style.display = "block";
        evt.currentTarget.className += " active";
    }
</script>
</div>
</body>
</html>
